<nav class="bg-white shadow-sm border-b border-gray-200 fixed w-full top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <!-- Left side - Logo and main nav -->
            <div class="flex items-center">
                <!-- Mobile menu button -->
                <button type="button" class="lg:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500" x-data x-on:click="$dispatch('toggle-sidebar')">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>

                <!-- Logo -->
                <div class="flex-shrink-0 flex items-center ml-4 lg:ml-0">
                    <a href="<?php echo e(route('dashboard')); ?>" class="text-2xl font-bold text-blue-600">
                        UniLink
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden lg:ml-10 lg:flex lg:space-x-8">
                    <a href="<?php echo e(route('dashboard')); ?>" class="text-gray-900 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium <?php echo e(request()->routeIs('dashboard') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                        Home
                    </a>
                    <a href="<?php echo e(route('announcements')); ?>" class="text-gray-900 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium <?php echo e(request()->routeIs('announcements*') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                        Campus Announcements
                    </a>
                    <a href="<?php echo e(route('organizations.index')); ?>" class="text-gray-900 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium <?php echo e(request()->routeIs('organizations*') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                        My Organizations
                    </a>
                    <a href="<?php echo e(route('scholarships.index')); ?>" class="text-gray-900 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium <?php echo e(request()->routeIs('scholarships*') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                        Find Scholarships
                    </a>
                </div>
            </div>

            <!-- Right side - Search, notifications, profile -->
            <div class="flex items-center space-x-4">
                <!-- Search -->
                <div class="hidden md:block">
                    <div class="relative">
                        <input type="text" placeholder="Search..." class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Notifications -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h5m5 0v6m0 0v6m0-6h6m-6 0H11" />
                        </svg>
                        <!-- Notification badge -->
                        <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"></span>
                    </button>

                    <!-- Notification dropdown -->
                    <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-50">
                        <div class="px-4 py-2 text-sm font-medium text-gray-900 border-b">Notifications</div>
                        <div class="max-h-64 overflow-y-auto">
                            <!-- Notification items will be loaded here -->
                            <div class="px-4 py-3 text-sm text-gray-500">No new notifications</div>
                        </div>
                    </div>
                </div>

                <!-- Profile dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <img class="h-8 w-8 rounded-full" src="<?php echo e(auth()->user()->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7F9CF5&background=EBF4FF'); ?>" alt="<?php echo e(auth()->user()->name); ?>">
                    </button>

                    <!-- Profile dropdown menu -->
                    <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                        <div class="px-4 py-2 text-sm text-gray-900 border-b">
                            <div class="font-medium"><?php echo e(auth()->user()->name); ?></div>
                            <div class="text-gray-500"><?php echo e(auth()->user()->email); ?></div>
                        </div>
                        <a href="<?php echo e(route('profile.edit')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                        <?php if(auth()->user()->hasManagementAccess()): ?>
                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Admin Panel</a>
                        <?php endif; ?>
                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Spacer for fixed header -->
<div class="h-16"></div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/layouts/unilink-header.blade.php ENDPATH**/ ?>