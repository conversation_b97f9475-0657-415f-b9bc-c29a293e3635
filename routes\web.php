<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('dashboard');
});

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    Route::get('/announcements', function () {
        return view('announcements.index');
    })->name('announcements');

    Route::get('/organizations', function () {
        return view('organizations.index');
    })->name('organizations.index');

    Route::get('/organizations/my', function () {
        return view('organizations.my');
    })->name('organizations.my');

    Route::get('/organizations/create', function () {
        return view('organizations.create');
    })->name('organizations.create')->middleware('role:admin,org_officer');

    Route::get('/scholarships', function () {
        return view('scholarships.index');
    })->name('scholarships.index');

    Route::get('/admin/dashboard', function () {
        return view('admin.dashboard');
    })->name('admin.dashboard')->middleware('role:admin,org_officer');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
