<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'UniLink') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-sans antialiased bg-gray-100">
        <div class="min-h-screen">
            <!-- Header Navigation -->
            @include('layouts.unilink-header')

            <!-- Main Container with 3-column layout -->
            <div class="flex max-w-screen-2xl mx-auto">
                <!-- Left Sidebar -->
                @include('layouts.unilink-sidebar')

                <!-- Central Feed -->
                <main class="flex-1 min-w-0 lg:ml-64 xl:ml-72 xl:mr-80 2xl:mr-96">
                    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                        {{ $slot }}
                    </div>
                </main>

                <!-- Right Sidebar -->
                @include('layouts.unilink-right-sidebar')
            </div>
        </div>

        <!-- Notification Popup -->
        @include('components.notification-popup')
    </body>
</html>
