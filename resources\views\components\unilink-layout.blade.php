<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'UniLink') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-sans antialiased bg-gray-50">
        <div class="min-h-screen">
            <!-- Header Navigation -->
            @include('layouts.unilink-header')

            <div class="flex">
                <!-- Left Sidebar -->
                @include('layouts.unilink-sidebar')

                <!-- Main Content Area -->
                <main class="flex-1 lg:ml-64 xl:ml-80">
                    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                        {{ $slot }}
                    </div>
                </main>

                <!-- Right Sidebar -->
                @include('layouts.unilink-right-sidebar')
            </div>
        </div>

        <!-- Notification Popup -->
        @include('components.notification-popup')
    </body>
</html>
