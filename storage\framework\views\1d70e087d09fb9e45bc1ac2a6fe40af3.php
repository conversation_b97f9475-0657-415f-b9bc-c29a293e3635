<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title><?php echo e(config('app.name', 'UniLink')); ?></title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    </head>
    <body class="font-sans antialiased bg-gray-100">
        <div class="min-h-screen">
            <!-- Header Navigation -->
            <?php echo $__env->make('layouts.unilink-header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            <!-- Main Container with 3-column layout -->
            <div class="flex max-w-screen-2xl mx-auto">
                <!-- Left Sidebar -->
                <?php echo $__env->make('layouts.unilink-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <!-- Central Feed -->
                <main class="flex-1 min-w-0 lg:ml-64 xl:ml-72 xl:mr-80 2xl:mr-96">
                    <!-- Feed Container -->
                    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-4 py-6">
                        <!-- Feed Header (if provided) -->
                        <?php if(isset($header)): ?>
                            <div class="mb-6">
                                <?php echo e($header); ?>

                            </div>
                        <?php endif; ?>

                        <!-- Main Feed Content -->
                        <div class="space-y-4">
                            <?php echo e($slot); ?>

                        </div>
                    </div>
                </main>

                <!-- Right Sidebar -->
                <?php echo $__env->make('layouts.unilink-right-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div x-data="{ open: false }" 
             x-on:toggle-sidebar.window="open = !open"
             x-show="open" 
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-75 z-30 lg:hidden"
             @click="$dispatch('toggle-sidebar')">
        </div>

        <!-- Notification Popup -->
        <?php echo $__env->make('components.notification-popup', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </body>
</html>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/feed-layout.blade.php ENDPATH**/ ?>